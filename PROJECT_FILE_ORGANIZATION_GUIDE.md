# 量化交易平台项目文件组织指南

## 📋 项目概述

本文档详细说明了量化交易平台的文件组织结构，帮助开发者快速理解项目架构，避免在开发过程中遇到配置和依赖问题。

## 🏗️ 项目根目录结构

```
quant014/
├── 📁 frontend/           # 前端应用 (Vue 3 + TypeScript + Vite)
├── 📁 backend/            # 后端服务 (FastAPI + Python)
├── 📁 docs/               # 项目文档
├── 📁 config/             # 配置文件
├── 📁 data/               # 数据存储
├── 📁 docker/             # Docker 容器配置
├── 📁 scripts/            # 自动化脚本
├── 📁 monitoring/         # 监控配置
├── 📁 mcp/                # MCP 测试工具
├── 📁 logs/               # 日志文件
├── 📁 archive/            # 归档文件
└── 📁 reports/            # 测试报告
```

## 🎯 核心目录详解

### 1. 📁 frontend/ - 前端应用

**技术栈**: Vue 3 + TypeScript + Vite + Element Plus + Pinia

**关键文件**:
```
frontend/
├── 📄 vite.config.js      # ⚠️ 关键配置文件
├── 📄 package.json        # 依赖管理
├── 📄 package-lock.json   # 依赖版本锁定
├── 📄 .env.local          # 本地环境变量
├── 📄 .env.production     # 生产环境变量
├── 📄 index.html          # 入口 HTML
├── 📁 src/
│   ├── 📄 main.ts         # 应用入口
│   ├── 📄 App.vue         # 根组件
│   ├── 📁 views/          # 页面组件
│   ├── 📁 components/     # 通用组件
│   ├── 📁 stores/         # Pinia 状态管理
│   ├── 📁 router/         # 路由配置
│   ├── 📁 api/            # API 接口
│   ├── 📁 utils/          # 工具函数
│   └── 📁 assets/         # 静态资源
└── 📁 dist/               # 构建输出
```

**⚠️ 重要配置说明**:
- `vite.config.js`: 编译目标必须设置为 `es2020` 或更高
- `.env.local`: 包含本地开发的 API 地址和 WebSocket 配置
- `package-lock.json`: 确保依赖版本一致性

### 2. 📁 backend/ - 后端服务

**技术栈**: FastAPI + SQLAlchemy + Alembic + Redis

**关键文件**:
```
backend/
├── 📄 requirements.txt    # Python 依赖
├── 📄 alembic.ini         # 数据库迁移配置
├── 📄 start_backend.py    # 启动脚本
├── 📁 app/
│   ├── 📄 main.py         # FastAPI 应用入口
│   ├── 📁 api/            # API 路由
│   ├── 📁 models/         # 数据模型
│   ├── 📁 services/       # 业务逻辑
│   ├── 📁 core/           # 核心配置
│   └── 📁 utils/          # 工具函数
├── 📁 migrations/         # 数据库迁移文件
├── 📁 config/             # 配置文件
└── 📁 logs/               # 后端日志
```

### 3. 📁 config/ - 配置管理

```
config/
├── 📁 environment/        # 环境配置
│   ├── 📄 development.yml
│   ├── 📄 production.yml
│   └── 📄 testing.yml
└── 📁 k8s/               # Kubernetes 配置
```

### 4. 📁 data/ - 数据存储

```
data/
├── 📁 historical/         # 历史数据
├── 📁 realtime/          # 实时数据
├── 📁 cache/             # 缓存数据
├── 📁 strategies/        # 策略数据
└── 📄 quantplatform.db   # SQLite 数据库
```

## 🚨 常见问题与解决方案

### 问题1: 前端页面空白

**症状**: 浏览器显示空白页面，控制台可能有编译错误

**原因**: 
1. Vite 配置中 `target` 设置过低 (如 `es2015`)
2. 缺少环境变量配置文件
3. 依赖版本不匹配

**解决方案**:
```javascript
// vite.config.js 确保设置
export default defineConfig({
  build: {
    target: 'es2020',  // 不要使用 es2015
  },
  esbuild: {
    target: 'es2020'   // 支持现代 JS 特性
  }
})
```

### 问题2: WebSocket 连接错误

**症状**: `Cannot read properties of undefined (reading 'on')`

**原因**: WebSocket 服务初始化时空值检查不足

**解决方案**: 在所有 WebSocket 操作前添加空值检查
```typescript
if (this.wsManager) {
  this.wsManager.on('connected', callback)
}
```

### 问题3: 环境变量配置缺失

**症状**: API 请求失败，WebSocket 连接失败

**解决方案**: 确保以下文件存在并配置正确
```bash
frontend/.env.local
frontend/.env.production
```

## 🔧 开发环境设置

### 前端开发
```bash
cd frontend
pnpm install --force  # 强制重新安装依赖
pnpm dev              # 启动开发服务器
```

### 后端开发
```bash
cd backend
pip install -r requirements.txt
python start_backend.py
```

## 📝 文件命名规范

### 前端文件
- 组件文件: `PascalCase.vue` (如 `TradingPanel.vue`)
- 工具文件: `camelCase.ts` (如 `formatUtils.ts`)
- 页面文件: `PascalCase.vue` (如 `Dashboard.vue`)

### 后端文件
- 模型文件: `snake_case.py` (如 `user_model.py`)
- 服务文件: `snake_case.py` (如 `trading_service.py`)
- API 文件: `snake_case.py` (如 `market_api.py`)

## 🚀 部署注意事项

### 生产环境检查清单
- [ ] 前端 `vite.config.js` 目标设置为 `es2020`
- [ ] 环境变量文件配置正确
- [ ] 依赖版本锁定文件存在
- [ ] WebSocket 服务空值检查完整
- [ ] 数据库迁移文件最新
- [ ] 日志配置适合生产环境

## 🛠️ 技术栈详细说明

### 前端技术栈
```json
{
  "框架": "Vue 3.4+",
  "语言": "TypeScript 5.0+",
  "构建工具": "Vite 6.3+",
  "UI库": "Element Plus 2.8+",
  "状态管理": "Pinia 2.2+",
  "路由": "Vue Router 4.4+",
  "HTTP客户端": "Axios 1.7+",
  "图表库": "ECharts 5.5+",
  "包管理器": "pnpm 9.0+"
}
```

### 后端技术栈
```json
{
  "框架": "FastAPI 0.104+",
  "语言": "Python 3.11+",
  "数据库": "SQLite/PostgreSQL",
  "ORM": "SQLAlchemy 2.0+",
  "迁移工具": "Alembic 1.13+",
  "缓存": "Redis 7.0+",
  "WebSocket": "python-socketio 5.10+",
  "任务队列": "Celery 5.3+"
}
```

## 🔍 深度故障排除指南

### 前端常见问题

#### 1. 编译错误: "Top-level await is not available"
```bash
# 错误信息
ERROR: Top-level await is not available in the configured target environment ("es2015")

# 解决方案
# 检查 vite.config.js 中的 target 设置
build: {
  target: 'es2020',  # 必须是 es2020 或更高
}
esbuild: {
  target: 'es2020'   # 必须与 build.target 一致
}
```

#### 2. 依赖安装问题
```bash
# 清理并重新安装
rm -rf node_modules package-lock.json
pnpm install --force

# 或者使用 npm
rm -rf node_modules package-lock.json
npm install --force
```

#### 3. 环境变量问题
```bash
# 检查必需的环境变量文件
frontend/.env.local          # 本地开发
frontend/.env.production     # 生产环境

# 示例配置
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws
VITE_APP_TITLE=量化交易平台
```

### 后端常见问题

#### 1. 数据库连接问题
```python
# 检查数据库配置
# backend/app/core/config.py
DATABASE_URL = "sqlite:///./data/quantplatform.db"

# 运行数据库迁移
cd backend
alembic upgrade head
```

#### 2. 依赖版本冲突
```bash
# 检查 Python 版本
python --version  # 应该是 3.11+

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

## 📋 开发工作流程

### 1. 新功能开发
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 前端开发
cd frontend
pnpm dev

# 3. 后端开发
cd backend
python start_backend.py

# 4. 测试
npm run test
python -m pytest

# 5. 提交代码
git add .
git commit -m "feat: 添加新功能"
```

### 2. 问题修复流程
```bash
# 1. 识别问题
# 检查浏览器控制台错误
# 检查后端日志 logs/app.log

# 2. 定位问题
# 前端: 检查 Network 标签页
# 后端: 检查 logs/error.log

# 3. 修复验证
# 重启服务验证修复效果
```

## 🚨 紧急故障处理

### 服务无法启动
```bash
# 1. 检查端口占用
netstat -ano | findstr :5173  # 前端
netstat -ano | findstr :8000  # 后端

# 2. 强制重启
# Windows
taskkill /f /im node.exe
taskkill /f /im python.exe

# Linux/Mac
pkill -f "vite"
pkill -f "uvicorn"

# 3. 清理缓存
cd frontend && rm -rf node_modules/.vite
cd backend && rm -rf __pycache__
```

### 数据库损坏
```bash
# 1. 备份当前数据库
cp data/quantplatform.db data/quantplatform.db.backup

# 2. 重新初始化
cd backend
alembic downgrade base
alembic upgrade head
```

## 📊 性能监控

### 前端性能
```javascript
// 在浏览器控制台运行
console.log('页面加载时间:', performance.timing.loadEventEnd - performance.timing.navigationStart, 'ms');
```

### 后端性能
```bash
# 检查后端响应时间
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:8000/api/v1/health"
```

## 📚 相关文档

- [API 文档](./docs/API_DOCUMENTATION.md)
- [部署指南](./docs/deployment/README.md)
- [开发指南](./docs/DEVELOPMENT_GUIDE.md)
- [故障排除](./docs/fixes/README.md)
- [性能优化](./docs/OPTIMIZATION.md)

## 🔗 有用的命令速查

### 前端命令
```bash
pnpm dev          # 开发服务器
pnpm build        # 生产构建
pnpm preview      # 预览构建结果
pnpm test         # 运行测试
pnpm lint         # 代码检查
```

### 后端命令
```bash
python start_backend.py     # 启动后端
alembic revision --autogenerate -m "描述"  # 创建迁移
alembic upgrade head        # 应用迁移
pytest                      # 运行测试
```

---

**最后更新**: 2025-01-12
**维护者**: 量化交易平台开发团队
**版本**: v1.0.0
