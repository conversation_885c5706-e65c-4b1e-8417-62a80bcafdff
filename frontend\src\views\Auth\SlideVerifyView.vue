<template>
  <div class="slide-verify-page">
    <div class="verify-container">
      <div class="verify-header">
        <h2>安全验证</h2>
        <p>为了确保账户安全，请完成以下滑块验证</p>
      </div>

      <div class="verify-content">
        <slide-verify
          :l="42"
          :r="10"
          :w="350"
          :h="200"
          :slider-text="sliderText"
          :accuracy="accuracy"
          :show="true"
          @success="onSuccess"
          @fail="onFail"
          @refresh="onRefresh"
          @again="onAgain"
          ref="slideVerify"
        ></slide-verify>

        <div class="verify-status" v-if="verifyStatus">
          <el-alert
            :title="verifyStatus.message"
            :type="verifyStatus.type"
            :closable="false"
            show-icon
          />
        </div>

        <div class="verify-actions">
          <el-button @click="goBack" size="large">
            返回登录
          </el-button>
          <el-button
            type="primary"
            @click="retryVerify"
            size="large"
            :disabled="!canRetry"
          >
            重试验证
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import SlideVerify from 'vue-monoplasty-slide-verify'
import { useUserStore } from '@/stores/modules/user'

// Router and Store
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// State
const slideVerify = ref()
const sliderText = ref('向右滑动滑块')
const accuracy = ref(5)
const canRetry = ref(true)
const verifyStatus = ref<{
  type: 'success' | 'warning' | 'error' | 'info'
  message: string
} | null>(null)

// Methods
const onSuccess = async () => {
  verifyStatus.value = {
    type: 'success',
    message: '验证成功！正在跳转到主页面...'
  }

  ElMessage.success('滑块验证成功！')

  try {
    // 延迟跳转以显示成功消息
    setTimeout(async () => {
      const redirect = route.query.redirect as string
      const targetUrl = redirect || '/dashboard'

      console.log('✅ 滑块验证成功，跳转到:', targetUrl)

      // 确保跳转成功
      await router.push(targetUrl)
    }, 1500)
  } catch (error) {
    console.error('❌ 跳转失败:', error)
    // 如果跳转失败，尝试跳转到默认页面
    await router.push('/dashboard')
  }
}

const onFail = () => {
  verifyStatus.value = {
    type: 'error',
    message: '验证失败，请重新尝试'
  }
  ElMessage.error('滑块验证失败，请重试')
  canRetry.value = true
}

const onRefresh = () => {
  verifyStatus.value = null
  canRetry.value = true
  ElMessage.info('验证已刷新，请重新滑动')
}

const onAgain = () => {
  verifyStatus.value = {
    type: 'warning',
    message: '检测到可疑行为，请手动完成验证'
  }
  ElMessage.warning('请手动完成滑块验证')
}

const retryVerify = () => {
  slideVerify.value?.reset()
  verifyStatus.value = null
  canRetry.value = false
}

const goBack = () => {
  router.push('/login')
}

// Mounted
onMounted(() => {
  // 检查是否已登录但未验证
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    router.push('/login')
  }
})
</script>

<style scoped>
.slide-verify-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.verify-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
}

.verify-header {
  margin-bottom: 32px;
}

.verify-header h2 {
  font-size: 28px;
  color: #333;
  margin: 0 0 8px 0;
}

.verify-header p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

.verify-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.verify-status {
  width: 100%;
}

.verify-actions {
  display: flex;
  gap: 16px;
  width: 100%;
  justify-content: center;
}

.verify-actions .el-button {
  min-width: 120px;
}

@media (max-width: 768px) {
  .verify-container {
    padding: 32px 20px;
  }

  .verify-actions {
    flex-direction: column;
  }

  .verify-actions .el-button {
    width: 100%;
  }
}
</style>
