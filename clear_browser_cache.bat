@echo off
echo ========================================
echo   浏览器缓存清理工具
echo   解决8001端口和旧缓存问题
echo ========================================
echo.

echo [1] 正在启动清理页面...
echo.

REM 获取当前目录
set CURRENT_DIR=%cd%

REM 检查清理页面是否存在
if not exist "%CURRENT_DIR%\frontend\public\clear-cache.html" (
    echo [错误] 清理页面不存在！
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo [2] 清理页面位置:
echo %CURRENT_DIR%\frontend\public\clear-cache.html
echo.

echo [3] 即将在浏览器中打开清理页面...
echo.
echo ----------------------------------------
echo   使用说明：
echo   1. 页面打开后，点击"快速清理"按钮
echo   2. 清理完成后，按Ctrl+Shift+R强制刷新
echo   3. 然后访问 http://localhost:5173
echo ----------------------------------------
echo.

REM 打开清理页面
start "" "file:///%CURRENT_DIR%\frontend\public\clear-cache.html"

echo [4] 清理页面已打开！
echo.
echo 如果页面没有自动打开，请手动访问:
echo file:///%CURRENT_DIR%\frontend\public\clear-cache.html
echo.
echo 或通过开发服务器访问:
echo http://localhost:5173/clear-cache.html
echo.

pause