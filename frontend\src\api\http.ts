/**
 * HTTP 请求客户端配置
 */
import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type AxiosResponse,
  type AxiosError,
  type InternalAxiosRequestConfig
} from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import router from '@/router'
import { useUserStore } from '@/stores/modules/user'
import type { ApiResponse, ApiError, RequestConfig } from '@/types/api'
import { securityInterceptorConfig } from './interceptors/security'
import { getGlobalErrorHandler } from '@/composables/useErrorHandler'
import { getGlobalFeedback } from '@/composables/useFeedback'

// 创建axios实例 - 优化超时配置
const http: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1',
  timeout: 15000, // 减少到15秒，避免408超时
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  // 添加更多配置以提高稳定性
  validateStatus: (status) => status < 500, // 接受所有小于500的状态码
  maxRedirects: 3,
  maxContentLength: 50 * 1024 * 1024, // 50MB
})

// 请求重试配置
interface RetryConfig {
  retries: number
  retryDelay: number
  retryCondition?: (error: AxiosError) => boolean
}

const defaultRetryConfig: RetryConfig = {
  retries: 2, // 减少重试次数，避免过长等待
  retryDelay: 800, // 减少重试延迟
  retryCondition: (error: AxiosError) => {
    // 只对网络错误和5xx错误重试，不对4xx错误重试
    return !error.response ||
           (error.response.status >= 500 && error.response.status < 600) ||
           error.code === 'ECONNABORTED' || // 超时
           error.code === 'NETWORK_ERROR'   // 网络错误
  }
}

// 添加重试功能
const addRetryInterceptor = (instance: AxiosInstance, config: RetryConfig = defaultRetryConfig) => {
  instance.interceptors.response.use(
    (response) => response,
    async (error: AxiosError) => {
      const { retries, retryDelay, retryCondition } = config
      const requestConfig = error.config as InternalAxiosRequestConfig & { __retryCount?: number }

      if (!requestConfig || !retryCondition?.(error)) {
        return Promise.reject(error)
      }

      requestConfig.__retryCount = requestConfig.__retryCount || 0

      if (requestConfig.__retryCount >= retries) {
        return Promise.reject(error)
      }

      requestConfig.__retryCount += 1

      await new Promise(resolve => setTimeout(resolve, retryDelay * requestConfig.__retryCount))

      return instance(requestConfig)
    }
  )
}

// 应用重试拦截器
addRetryInterceptor(http)

// 添加请求缓存
const requestCache = new Map<string, { data: any; timestamp: number; ttl: number }>()

const getCacheKey = (config: any): string => {
  return `${config.method}:${config.url}:${JSON.stringify(config.params || {})}`
}

const isCacheValid = (cacheEntry: { timestamp: number; ttl: number }): boolean => {
  return Date.now() - cacheEntry.timestamp < cacheEntry.ttl
}

// 请求拦截器
http.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 检查缓存（仅对GET请求）
    if (config.method === 'get') {
      const cacheKey = getCacheKey(config)
      const cacheEntry = requestCache.get(cacheKey)

      if (cacheEntry && isCacheValid(cacheEntry)) {
        // 返回缓存的数据
        console.log(`[HTTP Cache Hit] ${config.method?.toUpperCase()} ${config.url}`)
        return Promise.reject({
          isCache: true,
          data: cacheEntry.data,
          config
        })
      }
    }

    // 应用安全拦截器
    const secureConfig = securityInterceptorConfig.request(config as any)

    // 添加认证token
    const userStore = useUserStore()
    let token = userStore.token

    // 开发环境下，如果没有token，使用默认的开发token
    if (!token && import.meta.env.DEV) {
      token = 'dev-token-for-testing'
      console.warn('[DEV] Using development token for API requests')
    }

    if (token) {
      secureConfig.headers.Authorization = `Bearer ${token}`
    }

    console.log(`[HTTP Request] ${secureConfig.method?.toUpperCase()} ${secureConfig.url}`, {
      headers: secureConfig.headers,
      data: secureConfig.data,
      params: secureConfig.params
    })

    return secureConfig as InternalAxiosRequestConfig
  },
  (error: AxiosError) => {
    console.error('[HTTP Request Error]', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    // 缓存GET请求的响应
    if (response.config.method === 'get' && response.status === 200) {
      const cacheKey = getCacheKey(response.config)
      const ttl = response.config.url?.includes('/market/') ? 30000 : // 市场数据缓存30秒
                  response.config.url?.includes('/trading/') ? 10000 : // 交易数据缓存10秒
                  60000 // 其他数据缓存1分钟

      requestCache.set(cacheKey, {
        data: response,
        timestamp: Date.now(),
        ttl
      })

      // 清理过期缓存
      if (requestCache.size > 100) {
        const now = Date.now()
        for (const [key, entry] of requestCache.entries()) {
          if (!isCacheValid(entry)) {
            requestCache.delete(key)
          }
        }
      }
    }

    // 应用安全拦截器
    const secureResponse = securityInterceptorConfig.response(response)

    console.log(`[HTTP Response] ${secureResponse.config.method?.toUpperCase()} ${secureResponse.config.url}`, {
      status: secureResponse.status,
      data: secureResponse.data
    })

    // 检查业务状态码
    if (secureResponse.data && typeof secureResponse.data === 'object' && 'success' in secureResponse.data) {
      if (!secureResponse.data.success) {
        const error = new Error(secureResponse.data.message || '请求失败') as any
        error.code = secureResponse.data.code
        error.response = secureResponse
        return Promise.reject(error)
      }
    }

    return secureResponse
  },
  async (error: AxiosError<ApiError> | any) => {
    // 处理缓存命中的情况
    if (error.isCache) {
      console.log(`[HTTP Cache Hit] Returning cached data for ${error.config.url}`)
      return error.data
    }

    // 应用安全错误拦截器
    try {
      await securityInterceptorConfig.responseError(error)
    } catch (securityError) {
      // 如果安全拦截器抛出错误，使用原始错误
      error = securityError
    }
    console.error('[HTTP Response Error]', {
      status: error.response?.status,
      data: error.response?.data,
      config: error.config
    })

    const userStore = useUserStore()

    // 只处理特定的错误状态码，不显示重复的错误消息
    switch (error.response?.status) {
      case 401:
        // 未授权，清除token并跳转到登录页（静默处理，不显示消息）
        await userStore.logout()
        router.push('/login')
        break

      case 403:
        // 禁止访问（静默处理，让上层组件决定是否显示消息）
        break

      case 404:
        // 资源不存在（静默处理）
        break

      case 422:
        // 数据验证错误（静默处理）
        break

      case 429:
        // 请求过于频繁（静默处理）
        break

      case 500:
      case 502:
      case 503:
      case 504:
        // 服务器错误（静默处理）
        break

      default:
        // 其他错误（静默处理）
        break
    }

    return Promise.reject(error)
  }
)

// 生成请求ID
function generateRequestId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// 创建带有特定配置的请求函数
export function createRequest(config: RequestConfig = {}) {
  const instance = axios.create({
    ...http.defaults,
    timeout: config.timeout || http.defaults.timeout,
    headers: {
      ...http.defaults.headers,
      ...config.headers
    }
  })

  // 复制拦截器
  instance.interceptors.request = http.interceptors.request
  instance.interceptors.response = http.interceptors.response

  if (config.retries !== undefined) {
    addRetryInterceptor(instance, {
      retries: config.retries,
      retryDelay: config.retryDelay || 1000
    })
  }

  return instance
}

// 封装常用的HTTP方法
export const httpClient = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.get(url, config)
  },

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.post(url, data, config)
  },

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.put(url, data, config)
  },

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.patch(url, data, config)
  },

  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.delete(url, config)
  }
}

// 文件上传
export const uploadFile = async (
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<AxiosResponse<ApiResponse>> => {
  const formData = new FormData()
  formData.append('file', file)

  return http.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (progressEvent.total && onProgress) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

// 下载文件
export const downloadFile = async (url: string, filename?: string): Promise<void> => {
  try {
    const response = await http.get(url, {
      responseType: 'blob'
    })

    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('文件下载失败:', error)
    ElMessage.error('文件下载失败')
  }
}

// 批量请求
export const batchRequest = async <T = any>(
  requests: Array<() => Promise<AxiosResponse<ApiResponse<T>>>>
): Promise<Array<AxiosResponse<ApiResponse<T>> | Error>> => {
  return Promise.allSettled(requests.map(request => request())).then(results =>
    results.map(result =>
      result.status === 'fulfilled' ? result.value : result.reason
    )
  )
}

// 取消请求的控制器
export class RequestController {
  private controllers: Map<string, AbortController> = new Map()

  create(key: string): AbortController {
    this.cancel(key) // 取消之前的请求
    const controller = new AbortController()
    this.controllers.set(key, controller)
    return controller
  }

  cancel(key: string): void {
    const controller = this.controllers.get(key)
    if (controller) {
      controller.abort()
      this.controllers.delete(key)
    }
  }

  cancelAll(): void {
    this.controllers.forEach(controller => controller.abort())
    this.controllers.clear()
  }
}

export default http

// 为了兼容现有的导入语句，同时导出命名导出
export { http }
