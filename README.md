# 📊 量化投资平台 (Quantitative Trading Platform)

<div align="center">
  <img src="https://img.shields.io/badge/Python-3.10.13-blue.svg" alt="Python">
  <img src="https://img.shields.io/badge/FastAPI-0.104.1-green.svg" alt="FastAPI">
  <img src="https://img.shields.io/badge/Vue-3.4.0-brightgreen.svg" alt="Vue">
  <img src="https://img.shields.io/badge/TypeScript-5.8.0-blue.svg" alt="TypeScript">
  <img src="https://img.shields.io/badge/License-MIT-yellow.svg" alt="License">
</div>

## 🌟 项目概述

量化投资平台是一个基于现代化技术栈构建的专业量化交易系统，提供完整的量化投资解决方案。平台集成了实时行情展示、策略开发回测、模拟/实盘交易、风险管理等核心功能，适用于个人投资者、量化团队和金融机构。

### ✨ 核心特性

- 🚀 **高性能架构**: 基于FastAPI异步框架，支持高并发实时数据处理
- 📈 **实时行情**: WebSocket推送，毫秒级数据更新，专业K线图表
- 🎯 **策略引擎**: 完整的策略开发、回测、优化和执行系统
- 💼 **交易执行**: 支持模拟交易和实盘对接，订单管理完善
- 🛡️ **风险控制**: 实时风险监控，多维度风险指标计算
- 📊 **数据分析**: 丰富的技术指标，专业的量化分析工具
- 🔐 **安全可靠**: JWT认证，权限控制，金融级安全保障
- 📱 **响应式设计**: 支持PC、平板、手机多端访问

## 🏗️ 技术架构

### 后端技术栈
- **框架**: FastAPI 0.104.1 (异步高性能Web框架)
- **语言**: Python 3.10.13 (推荐版本，TA-Lib兼容)
- **数据库**: SQLAlchemy 2.0.23 + PostgreSQL/SQLite
- **缓存**: Redis 5.0.1
- **任务队列**: Celery 5.3.4
- **认证**: JWT (python-jose)
- **WebSocket**: FastAPI原生WebSocket

### 前端技术栈
- **框架**: Vue 3.4.0 + Composition API
- **语言**: TypeScript 5.8.0
- **构建工具**: Vite 6.3.5
- **UI组件**: Element Plus 2.10.1
- **图表**: ECharts 5.6.0
- **状态管理**: Pinia 2.1.7
- **HTTP客户端**: Axios 1.9.0
- **包管理**: pnpm (推荐)

### 监控与部署
- **监控**: Prometheus + Grafana
- **容器化**: Docker + Docker Compose
- **CI/CD**: GitHub Actions
- **日志**: Structlog + ELK Stack

## 📁 项目结构

```
quant014/
├── backend/                    # 后端服务
│   ├── app/                   # 应用主目录
│   │   ├── api/               # API路由 (25+ endpoints)
│   │   │   └── v1/           # v1版本API
│   │   ├── core/             # 核心功能 (20+ files)
│   │   ├── services/         # 业务服务 (60+ services)
│   │   ├── models/           # 数据模型
│   │   ├── schemas/          # 数据验证
│   │   ├── middleware/       # 中间件
│   │   ├── monitoring/       # 监控系统
│   │   └── utils/            # 工具函数
│   ├── migrations/            # 数据库迁移
│   ├── tests/                # 测试文件
│   └── requirements.txt      # Python依赖
│
├── frontend/                  # 前端应用
│   ├── src/
│   │   ├── api/              # API接口层
│   │   ├── components/       # 组件库 (80+ components)
│   │   ├── views/            # 页面视图 (25+ pages)
│   │   ├── stores/           # 状态管理
│   │   ├── router/           # 路由配置
│   │   ├── composables/      # 组合函数
│   │   └── utils/            # 工具函数
│   ├── public/               # 静态资源
│   └── package.json          # 前端依赖
│
├── docker/                    # Docker配置
├── docs/                     # 项目文档 (100+ docs)
├── scripts/                  # 脚本工具
├── data/                     # 数据目录
└── config/                   # 配置文件
```

## 🚀 快速开始

### 环境要求

- Python 3.10.13 (重要：不要使用3.12+，TA-Lib不兼容)
- Node.js >= 18.0.0
- Redis (可选，用于缓存)
- PostgreSQL (生产环境) 或 SQLite (开发环境)

### 安装步骤

#### 1. 克隆项目
```bash
git clone https://github.com/your-org/quant-platform.git
cd quant014
```

#### 2. 后端安装

##### Windows环境
```batch
# 创建虚拟环境
python -m venv backend\venv

# 激活虚拟环境
backend\venv\Scripts\activate

# 安装依赖
cd backend
pip install -r requirements.txt

# 初始化数据库
python scripts\init_db.py
```

##### Linux/Mac环境
```bash
# 创建虚拟环境
python3 -m venv backend/venv

# 激活虚拟环境
source backend/venv/bin/activate

# 安装依赖
cd backend
pip install -r requirements.txt

# 初始化数据库
python scripts/init_db.py
```

#### 3. 前端安装
```bash
cd frontend

# 使用pnpm安装依赖（推荐）
pnpm install

# 或使用npm
npm install
```

### 启动服务

#### 方式一：一键启动（Windows）
```batch
# 启动后端
backend\start_windows.bat

# 新开终端，启动前端
cd frontend
pnpm dev
```

#### 方式二：手动启动
```bash
# 启动后端
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 启动前端
cd frontend
pnpm dev
```

#### 方式三：Docker启动
```bash
docker-compose up -d
```

### 访问地址

- 🌐 **前端应用**: http://localhost:5173
- 📡 **后端API**: http://localhost:8000
- 📚 **API文档**: http://localhost:8000/docs
- 📊 **监控面板**: http://localhost:3000 (Grafana)

### 默认账号

- **管理员账号**: admin / admin
- **演示账号**: demo / demo123

## 💡 功能模块

### 1. 用户认证与权限
- ✅ 用户注册/登录/登出
- ✅ JWT Token认证
- ✅ 多级权限控制
- ✅ 密码加密存储
- ✅ 会话管理

### 2. 市场数据
- ✅ 实时行情展示
- ✅ K线图表（1分钟、5分钟、日线等）
- ✅ 技术指标计算（MA、MACD、RSI等）
- ✅ 历史数据查询
- ✅ 股票筛选器
- ✅ 板块行情

### 3. 交易执行
- ✅ 快速下单
- ✅ 订单管理（查询、撤销、修改）
- ✅ 持仓管理
- ✅ 成交查询
- ✅ 资金管理
- ✅ 模拟交易

### 4. 策略管理
- ✅ 策略开发IDE
- ✅ 策略模板库
- ✅ 参数优化
- ✅ 策略监控
- ✅ 信号提醒
- ✅ 策略评估

### 5. 回测系统
- ✅ 历史数据回测
- ✅ 多策略对比
- ✅ 回测报告生成
- ✅ 绩效分析
- ✅ 风险评估
- ✅ 参数优化

### 6. 风险管理
- ✅ 实时风险监控
- ✅ VaR计算
- ✅ 最大回撤控制
- ✅ 仓位管理
- ✅ 止盈止损
- ✅ 风险报告

### 7. 数据分析
- ✅ 收益分析
- ✅ 归因分析
- ✅ 相关性分析
- ✅ 技术指标
- ✅ 统计分析
- ✅ 报表导出

## 📡 API文档

### 认证接口
| 方法 | 路径 | 说明 |
|------|------|------|
| POST | `/api/v1/auth/register` | 用户注册 |
| POST | `/api/v1/auth/login` | 用户登录 |
| POST | `/api/v1/auth/logout` | 用户登出 |
| POST | `/api/v1/auth/refresh` | 刷新Token |
| GET | `/api/v1/auth/profile` | 获取用户信息 |

### 市场数据接口
| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/v1/market/stocks` | 获取股票列表 |
| GET | `/api/v1/market/realtime/{symbol}` | 获取实时行情 |
| GET | `/api/v1/market/kline/{symbol}` | 获取K线数据 |
| GET | `/api/v1/market/indicators/{symbol}` | 获取技术指标 |
| GET | `/api/v1/market/history/{symbol}` | 获取历史数据 |

### 交易接口
| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/v1/trading/positions` | 查询持仓 |
| GET | `/api/v1/trading/orders` | 查询订单 |
| POST | `/api/v1/trading/orders` | 创建订单 |
| PUT | `/api/v1/trading/orders/{id}` | 修改订单 |
| DELETE | `/api/v1/trading/orders/{id}` | 撤销订单 |
| GET | `/api/v1/trading/trades` | 查询成交 |

### 策略接口
| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/v1/strategies` | 获取策略列表 |
| POST | `/api/v1/strategies` | 创建策略 |
| GET | `/api/v1/strategies/{id}` | 获取策略详情 |
| PUT | `/api/v1/strategies/{id}` | 更新策略 |
| DELETE | `/api/v1/strategies/{id}` | 删除策略 |
| POST | `/api/v1/strategies/{id}/run` | 运行策略 |
| POST | `/api/v1/strategies/{id}/stop` | 停止策略 |

### 回测接口
| 方法 | 路径 | 说明 |
|------|------|------|
| POST | `/api/v1/backtest/run` | 运行回测 |
| GET | `/api/v1/backtest/results/{id}` | 获取回测结果 |
| GET | `/api/v1/backtest/history` | 回测历史 |
| POST | `/api/v1/backtest/optimize` | 参数优化 |

### 风险管理接口
| 方法 | 路径 | 说明 |
|------|------|------|
| GET | `/api/v1/risk/metrics` | 获取风险指标 |
| GET | `/api/v1/risk/limits` | 获取风险限制 |
| POST | `/api/v1/risk/limits` | 设置风险限制 |
| GET | `/api/v1/risk/alerts` | 获取风险告警 |

### WebSocket接口
| 路径 | 说明 |
|------|------|
| `/ws/market` | 实时行情推送 |
| `/ws/trading` | 交易状态推送 |
| `/ws/strategy` | 策略信号推送 |
| `/ws/risk` | 风险告警推送 |

## ⚙️ 配置说明

### 环境变量配置
创建 `.env` 文件：
```env
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost/quantdb
REDIS_URL=redis://localhost:6379/0

# JWT配置
SECRET_KEY=your-secret-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION=3600

# API密钥
TUSHARE_TOKEN=your-tushare-token
AKSHARE_TOKEN=your-akshare-token

# 监控配置
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# 日志级别
LOG_LEVEL=INFO
```

### 数据源配置
支持多种数据源：
- Tushare (推荐)
- AkShare
- 新浪财经
- 东方财富
- 自定义数据源

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd backend
pytest tests/

# 前端测试
cd frontend
pnpm test

# E2E测试
pnpm test:e2e
```

### 测试覆盖率
```bash
# 生成覆盖率报告
pytest --cov=app tests/
```

## 📊 性能指标

- **API响应时间**: < 100ms (平均)
- **WebSocket延迟**: < 50ms
- **并发用户**: 1000+
- **数据处理**: 10000+ ticks/秒
- **内存占用**: < 500MB
- **CPU使用率**: < 20%

## 🔧 开发指南

### 代码规范
- Python: PEP 8 + Black格式化
- TypeScript: ESLint + Prettier
- Git: Conventional Commits

### 分支策略
- `main`: 生产分支
- `develop`: 开发分支
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

### 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式
refactor: 重构
test: 测试
chore: 构建工具
```

## 🚨 故障排查

### 常见问题

#### 1. Python版本问题
```
错误: TA-Lib安装失败
解决: 使用Python 3.10.13，不要使用3.12+
```

#### 2. 依赖安装失败
```
错误: psycopg2安装失败
解决: Windows安装psycopg2-binary替代
```

#### 3. 前端启动失败
```
错误: pnpm: command not found
解决: npm install -g pnpm
```

#### 4. 数据库连接失败
```
错误: Connection refused
解决: 检查数据库服务是否启动
```

## 📈 路线图

### 短期计划 (1-2月)
- [ ] 接入更多实时数据源
- [ ] 优化回测引擎性能
- [ ] 增加机器学习策略
- [ ] 移动端APP开发

### 中期计划 (3-6月)
- [ ] 分布式架构改造
- [ ] 高频交易支持
- [ ] 期权交易功能
- [ ] 算法交易引擎

### 长期计划 (6-12月)
- [ ] AI智能投顾
- [ ] 量化社区平台
- [ ] 云端策略市场
- [ ] 国际市场支持

## 🤝 贡献指南

欢迎贡献代码、报告问题或提出建议！

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'feat: Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用 MIT 许可证，详见 [LICENSE](LICENSE) 文件。

## 👥 团队

- 架构设计：Quant Platform Team
- 后端开发：Backend Team
- 前端开发：Frontend Team
- 量化策略：Strategy Team
- 运维支持：DevOps Team

## 📞 联系方式

- 项目主页：https://github.com/quant-platform
- 问题反馈：https://github.com/quant-platform/issues
- 邮箱：<EMAIL>
- 文档：https://docs.quantplatform.com

## 🙏 致谢

感谢以下开源项目：
- [FastAPI](https://fastapi.tiangolo.com/)
- [Vue.js](https://vuejs.org/)
- [ECharts](https://echarts.apache.org/)
- [Element Plus](https://element-plus.org/)
- [TA-Lib](https://github.com/mrjbq7/ta-lib)

---

<div align="center">
  <b>打造专业的量化投资平台</b><br>
  Made with ❤️ by Quant Platform Team
</div>