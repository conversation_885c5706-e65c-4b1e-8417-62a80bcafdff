# 🚨 故障排除快速参考卡

## 🎯 最常见问题 (90% 的问题都在这里)

### 1. 前端页面空白 ⚡
```bash
# 症状: 浏览器显示空白页面
# 原因: Vite 配置目标环境过低

# 🔧 快速修复
# 编辑 frontend/vite.config.js
build: { target: 'es2020' }
esbuild: { target: 'es2020' }

# 重启开发服务器
cd frontend && pnpm dev
```

### 2. WebSocket 错误 ⚡
```bash
# 症状: Cannot read properties of undefined (reading 'on')
# 原因: WebSocket 服务空值检查缺失

# 🔧 快速修复
# 在所有 this.wsManager 使用前添加检查
if (this.wsManager) {
  this.wsManager.on('connected', callback)
}
```

### 3. 依赖安装失败 ⚡
```bash
# 症状: npm install 或 pnpm install 失败
# 🔧 快速修复
cd frontend
rm -rf node_modules package-lock.json
pnpm install --force
```

### 4. 环境变量缺失 ⚡
```bash
# 症状: API 请求失败，404 错误
# 🔧 快速修复
# 确保存在这些文件:
frontend/.env.local
frontend/.env.production

# 内容示例:
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws
```

## 🔍 问题诊断流程

### Step 1: 确定问题类型
```bash
# 前端问题 → 浏览器控制台有错误
# 后端问题 → 网络请求失败
# 配置问题 → 服务无法启动
```

### Step 2: 检查关键文件
```bash
# 前端关键文件
frontend/vite.config.js     # 编译配置
frontend/.env.local         # 环境变量
frontend/package.json       # 依赖版本

# 后端关键文件
backend/requirements.txt    # Python 依赖
backend/app/main.py        # 应用入口
backend/config/            # 配置文件
```

### Step 3: 查看日志
```bash
# 前端日志 → 浏览器控制台
# 后端日志 → logs/app.log, logs/error.log
# 系统日志 → 终端输出
```

## ⚡ 一键修复脚本

### 前端重置脚本
```bash
#!/bin/bash
# 保存为 frontend/reset.sh
cd frontend
echo "🔄 重置前端环境..."
rm -rf node_modules package-lock.json .vite
pnpm install --force
echo "✅ 前端环境重置完成"
pnpm dev
```

### 后端重置脚本
```bash
#!/bin/bash
# 保存为 backend/reset.sh
cd backend
echo "🔄 重置后端环境..."
rm -rf __pycache__ .pytest_cache
pip install -r requirements.txt --force-reinstall
echo "✅ 后端环境重置完成"
python start_backend.py
```

## 🚨 紧急情况处理

### 服务器无响应
```bash
# 1. 强制结束进程
# Windows
taskkill /f /im node.exe
taskkill /f /im python.exe

# Linux/Mac
pkill -f "vite"
pkill -f "uvicorn"

# 2. 重启服务
cd frontend && pnpm dev &
cd backend && python start_backend.py &
```

### 数据库锁定
```bash
# SQLite 数据库锁定
cd backend
python -c "
import sqlite3
conn = sqlite3.connect('data/quantplatform.db')
conn.execute('BEGIN IMMEDIATE;')
conn.rollback()
conn.close()
print('数据库锁定已解除')
"
```

### 端口被占用
```bash
# 查找占用进程
netstat -ano | findstr :5173  # 前端端口
netstat -ano | findstr :8000  # 后端端口

# 结束进程 (Windows)
taskkill /PID <PID> /F

# 结束进程 (Linux/Mac)
kill -9 <PID>
```

## 🔧 配置文件模板

### frontend/.env.local
```env
# 本地开发环境配置
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws
VITE_APP_TITLE=量化交易平台
VITE_APP_DESCRIPTION=专业量化交易平台
VITE_APP_VERSION=1.0.0
NODE_ENV=development
```

### frontend/vite.config.js (关键部分)
```javascript
export default defineConfig({
  build: {
    target: 'es2020',  // 🚨 必须是 es2020 或更高
  },
  esbuild: {
    target: 'es2020'   // 🚨 必须与 build.target 一致
  },
  server: {
    port: 5173,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true
      }
    }
  }
})
```

## 📞 获取帮助

### 问题报告模板
```markdown
## 问题描述
[简要描述问题]

## 复现步骤
1. 
2. 
3. 

## 期望结果
[描述期望的结果]

## 实际结果
[描述实际发生的情况]

## 环境信息
- 操作系统: 
- Node.js 版本: 
- Python 版本: 
- 浏览器: 

## 错误日志
```
[粘贴相关错误日志]
```

## 已尝试的解决方案
[列出已经尝试过的解决方案]
```

### 联系方式
- 📧 技术支持: <EMAIL>
- 📱 紧急热线: +86-xxx-xxxx-xxxx
- 💬 内部群组: 量化平台技术支持群

---

**🔖 收藏此页面**: 遇到问题时第一时间查看  
**⏰ 更新频率**: 每周更新  
**📅 最后更新**: 2025-01-12
