/**
 * 简化的应用主入口文件
 * 基于参考项目的最佳实践，移除可能导致问题的复杂逻辑
 * 修复路由注入错误和组件渲染问题
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

import App from './App.vue'
import router from './router'
import { config } from '@/config'
import i18n from '@/locales'
import http from '@/api/http'

// 设置API基础URL
http.defaults.baseURL = config.api.baseURL

// 导入全局样式
import '@/assets/styles/element.scss'
import '@/assets/styles/index.scss'

// 导入Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'

// 导入Element Plus图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入ECharts
import * as echarts from 'echarts'

// 导入全局组件
import GlobalComponents from '@/components'

// 导入滑块验证组件
import SlideVerify from 'vue-monoplasty-slide-verify'

// 创建Vue应用实例
const app = createApp(App)

// =======================
// Pinia状态管理配置
// =======================
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)

// =======================
// 路由配置
// =======================
app.use(router)

// =======================
// 国际化配置
// =======================
app.use(i18n)

// =======================
// Element Plus配置
// =======================
app.use(ElementPlus)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// =======================
// 全局组件注册
// =======================
app.use(GlobalComponents)

// =======================
// 滑块验证组件
// =======================
app.use(SlideVerify)

// =======================
// 全局属性配置
// =======================
app.config.globalProperties.$echarts = echarts
app.config.globalProperties.$http = http

// =======================
// 错误处理 - 增强版本
// =======================
app.config.errorHandler = (err, instance, info) => {
  console.error('🚨 全局错误捕获:', err)
  console.error('📍 错误信息:', info)
  console.error('🔍 组件实例:', instance)

  // 在开发环境显示详细错误
  if (config.app.isDev) {
    console.error('🛠️ 开发环境详细信息:', {
      error: err,
      info,
      instance,
      stack: err instanceof Error ? err.stack : 'No stack trace'
    })
  }

  // 防止路由相关错误导致页面崩溃
  if (info.includes('router') || info.includes('route')) {
    console.warn('🔄 检测到路由相关错误，尝试恢复...')
    // 不阻止错误传播，但记录特殊处理
  }
}

// Vue警告处理 - 增强版本
app.config.warnHandler = (msg, instance, trace) => {
  if (config.app.isDev) {
    console.warn('⚠️ Vue警告:', msg)
    console.warn('📊 组件追踪:', trace)

    // 特别处理路由相关警告
    if (msg.includes('injection') && msg.includes('route')) {
      console.warn('🔧 路由注入警告 - 这可能是正常的延迟初始化')
    }
  }
}

// =======================
// 开发环境调试工具
// =======================
if (config.app.isDev) {
  // 暴露调试工具到全局
  (window as any).__APP_DEBUG__ = {
    app,
    router,
    pinia,
    config,
    version: config.app.version
  }

  console.log('🛠️ 开发调试工具已启用，可通过 window.__APP_DEBUG__ 访问')
  console.log(`📱 应用版本: ${config.app.version}`)
  console.log(`🌐 API地址: ${config.api.baseURL}`)
  console.log(`🔌 WebSocket地址: ${config.api.websocket.url}`)
}

// =======================
// PWA支持 (仅生产环境启用)
// =======================
// 开发环境禁用Service Worker以避免缓存问题
if (config.app.enablePWA && 'serviceWorker' in navigator && !config.app.isDev) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('✅ Service Worker 注册成功:', registration.scope)
      })
      .catch((error) => {
        console.error('❌ Service Worker 注册失败:', error)
      })
  })
} else if (config.app.isDev && 'serviceWorker' in navigator) {
  // 开发环境下注销所有Service Worker
  navigator.serviceWorker.getRegistrations().then(registrations => {
    registrations.forEach(registration => {
      registration.unregister()
      console.log('🔧 开发环境已注销Service Worker:', registration.scope)
    })
  })
}

// =======================
// 应用启动
// =======================
app.mount('#app')

console.log(`🚀 ${config.app.name} v${config.app.version} 启动成功!`)
console.log(`🌍 运行环境: ${config.app.isDev ? '开发' : '生产'}`)
console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`)

export default app
