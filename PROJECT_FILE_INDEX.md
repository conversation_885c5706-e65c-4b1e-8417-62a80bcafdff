# 📁 项目文件分类索引

## 🎯 快速导航

| 分类 | 描述 | 关键文件 |
|------|------|----------|
| [🚀 核心应用](#-核心应用) | 前后端主要代码 | `frontend/src/`, `backend/app/` |
| [⚙️ 配置文件](#️-配置文件) | 项目配置和环境设置 | `vite.config.js`, `.env.*` |
| [📚 文档资料](#-文档资料) | 项目文档和说明 | `docs/`, `README.md` |
| [🔧 工具脚本](#-工具脚本) | 自动化和部署脚本 | `scripts/`, `Makefile` |
| [🧪 测试相关](#-测试相关) | 测试文件和报告 | `tests/`, `*test*.js` |
| [📦 依赖管理](#-依赖管理) | 包管理和依赖配置 | `package.json`, `requirements.txt` |
| [🗄️ 数据存储](#️-数据存储) | 数据库和缓存文件 | `data/`, `*.db` |
| [📊 监控日志](#-监控日志) | 日志和监控配置 | `logs/`, `monitoring/` |

## 🚀 核心应用

### 前端应用 (frontend/)
```
📁 frontend/
├── 📄 src/main.ts              # 🔥 应用入口 - 最重要
├── 📄 src/App.vue              # 🔥 根组件
├── 📄 vite.config.js           # 🔥 构建配置 - 经常出问题
├── 📄 index.html               # HTML 入口
├── 📁 src/views/               # 页面组件
│   ├── 📄 Dashboard.vue        # 仪表板
│   ├── 📄 Trading.vue          # 交易页面
│   └── 📄 Market.vue           # 行情页面
├── 📁 src/components/          # 通用组件
├── 📁 src/stores/              # 状态管理
├── 📁 src/router/              # 路由配置
└── 📁 src/api/                 # API 接口
```

### 后端服务 (backend/)
```
📁 backend/
├── 📄 app/main.py              # 🔥 FastAPI 应用入口
├── 📄 start_backend.py         # 🔥 启动脚本
├── 📁 app/api/                 # API 路由
│   ├── 📄 market.py            # 行情接口
│   ├── 📄 trading.py           # 交易接口
│   └── 📄 user.py              # 用户接口
├── 📁 app/models/              # 数据模型
├── 📁 app/services/            # 业务逻辑
└── 📁 app/core/                # 核心配置
```

## ⚙️ 配置文件

### 🔥 关键配置 (经常需要修改)
```
📄 frontend/vite.config.js      # Vite 构建配置
📄 frontend/.env.local          # 本地环境变量
📄 frontend/.env.production     # 生产环境变量
📄 frontend/package.json        # 前端依赖
📄 backend/requirements.txt     # 后端依赖
📄 backend/alembic.ini          # 数据库迁移配置
```

### 🛠️ 工具配置
```
📄 frontend/tsconfig.json       # TypeScript 配置
📄 frontend/eslint.config.js    # ESLint 配置
📄 frontend/tailwind.config.js  # Tailwind CSS 配置
📄 backend/config/settings.py   # 后端设置
📄 docker-compose.yml           # Docker 编排
```

## 📚 文档资料

### 📖 主要文档
```
📄 README.md                           # 项目概述
📄 PROJECT_FILE_ORGANIZATION_GUIDE.md  # 🔥 文件组织指南
📄 TROUBLESHOOTING_QUICK_REFERENCE.md  # 🔥 故障排除指南
📁 docs/                               # 详细文档目录
├── 📄 API_DOCUMENTATION.md            # API 文档
├── 📄 DEVELOPMENT_GUIDE.md            # 开发指南
└── 📄 DEPLOYMENT_GUIDE.md             # 部署指南
```

### 📋 报告文档
```
📄 FRONTEND_COMPLETE_FIX.md            # 前端修复报告
📄 PROJECT_COMPLETION_SUMMARY.md       # 项目完成总结
📄 CRITICAL_ISSUES_FIXED_REPORT.md     # 关键问题修复报告
```

## 🔧 工具脚本

### 🚀 启动脚本
```
📄 start.sh                    # Linux/Mac 启动脚本
📄 scripts/start.bat           # Windows 启动脚本
📄 backend/start_backend.py    # 后端启动脚本
📄 scripts/start_windows.bat   # Windows 专用启动
```

### 🛠️ 工具脚本
```
📁 scripts/
├── 📄 cleanup_project.py      # 项目清理
├── 📄 deploy.sh               # 部署脚本
├── 📄 test-all.sh             # 全量测试
└── 📄 setup_python_environment.sh  # Python 环境设置
```

## 🧪 测试相关

### 🔍 测试文件
```
📁 frontend/tests/              # 前端测试
📁 backend/tests/               # 后端测试
📄 comprehensive_api_test.js    # API 综合测试
📄 puppeteer_ui_test.js         # UI 自动化测试
📄 login_test.html              # 登录测试页面
```

### 📊 测试报告
```
📄 *_test_results_*.json       # 测试结果文件
📄 FINAL_UI_TESTING_REPORT.md  # UI 测试报告
📄 COMPREHENSIVE_MCP_TESTING_REPORT.md  # MCP 测试报告
```

## 📦 依赖管理

### 📋 依赖文件
```
📄 frontend/package.json        # 🔥 前端依赖定义
📄 frontend/package-lock.json   # 🔥 前端依赖锁定
📄 frontend/pnpm-lock.yaml      # pnpm 锁定文件
📄 backend/requirements.txt     # 🔥 后端依赖
📄 package.json                 # 根目录包配置
```

## 🗄️ 数据存储

### 💾 数据文件
```
📁 data/
├── 📄 quantplatform.db        # 🔥 主数据库
├── 📁 historical/             # 历史数据
├── 📁 realtime/               # 实时数据
├── 📁 cache/                  # 缓存数据
└── 📁 strategies/             # 策略数据
```

### 🗃️ 数据库相关
```
📁 backend/migrations/          # 数据库迁移文件
📄 backend/alembic.ini          # 迁移配置
📄 backend/quant_platform.db    # 后端数据库
```

## 📊 监控日志

### 📝 日志文件
```
📁 logs/
├── 📄 app.log                 # 应用日志
├── 📄 error.log               # 错误日志
├── 📄 trading.log             # 交易日志
└── 📁 archive/                # 归档日志
```

### 📈 监控配置
```
📁 monitoring/
├── 📄 prometheus.yml          # Prometheus 配置
├── 📄 alert_rules.yml         # 告警规则
└── 📁 alertmanager/           # 告警管理器配置
```

## 🗂️ 归档文件

### 📦 归档目录
```
📁 archive/                    # 归档文件目录
├── 📁 old_configs/            # 旧配置文件
├── 📁 reports/                # 历史报告
├── 📁 temp/                   # 临时文件
└── 📁 backup_before_fix/      # 修复前备份
```

## 🔍 文件查找技巧

### 按功能查找
```bash
# 查找配置文件
find . -name "*.config.*" -o -name "*.env*" -o -name "vite.config.*"

# 查找测试文件
find . -name "*test*" -o -name "*spec*"

# 查找文档文件
find . -name "*.md" | grep -E "(README|GUIDE|DOC)"
```

### 按问题类型查找
```bash
# 前端构建问题 → 查看
frontend/vite.config.js
frontend/package.json
frontend/.env.local

# 后端启动问题 → 查看
backend/start_backend.py
backend/requirements.txt
backend/app/main.py

# 数据库问题 → 查看
backend/alembic.ini
backend/migrations/
data/quantplatform.db
```

---

**💡 使用提示**: 
- 🔥 标记的文件是最常修改的
- 遇到问题时先查看对应分类的关键文件
- 使用 Ctrl+F 快速搜索文件名

**📅 最后更新**: 2025-01-12
