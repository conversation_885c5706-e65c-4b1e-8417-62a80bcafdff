"""
量化投资后端主应用
基于FastAPI的高性能异步API服务
集成增强的错误处理和日志系统
"""

import logging
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Any, Dict

import uvicorn
from fastapi import APIRouter, FastAPI, HTTPException, Request, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.openapi.docs import get_redoc_html, get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi.responses import JSONResponse, FileResponse
from fastapi.staticfiles import StaticFiles

from app.api.v1 import api_router as v1_router
from app.core.config import get_settings
from app.core.cors_config import configure_cors, debug_cors_config
from app.core.database import DatabaseManager, cleanup_db, init_db
from app.core.monitoring import <PERSON><PERSON><PERSON><PERSON>, MetricsCollector, PerformanceMiddleware
from app.core.websocket import ConnectionManager
from app.core.websocket_enhanced import enhanced_manager

# 导入增强的日志和异常处理系统
from app.core.logging_config import setup_logging, get_contextual_logger
from app.middleware.exception_middleware import ExceptionHandlerMiddleware
from app.middleware.request_logging_middleware import create_request_logging_middleware
from app.core.exceptions import BaseCustomException
from app.core.error_monitoring import setup_error_monitoring

from app.middleware.security_middleware import (
    CORSSecurityMiddleware,
    LoginSecurityMiddleware,
    SecurityMiddleware,
)
from app.middleware.response_optimization_middleware import setup_response_optimization
from app.monitoring.middleware import setup_monitoring_middleware
from app.monitoring.startup import (
    health_check,
    liveness_check,
    readiness_check,
    setup_monitoring_startup,
)
from app.monitoring.version_check import router as version_router, log_version_warning
from app.utils.exceptions import QuantPlatformException

# 获取配置
settings = get_settings()

# 初始化增强的日志系统
setup_logging()
logger = get_contextual_logger(__name__)

# 初始化全局组件
metrics_collector = MetricsCollector()
health_checker = HealthChecker()
websocket_manager = ConnectionManager()


async def check_database_health() -> bool:
    """数据库健康检查"""
    try:
        db_manager = DatabaseManager()
        return await db_manager.health_check()
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


async def check_websocket_health() -> bool:
    """WebSocket健康检查"""
    try:
        return websocket_manager.is_healthy()
    except Exception as e:
        logger.error(f"WebSocket health check failed: {e}")
        return False


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理 - 集成增强的日志和错误处理"""
    # 启动时执行
    logger.info("Application startup initiated",
               project_name=settings.PROJECT_NAME,
               version=settings.VERSION,
               environment=getattr(settings, "ENVIRONMENT", "development"))

    # Python 版本兼容性检查
    log_version_warning()
    logger.info("Python version compatibility check completed")

    try:
        # 初始化监控系统
        await metrics_collector.initialize()
        logger.info("Metrics collector initialized successfully")
        
        # 初始化错误监控系统
        error_monitor = setup_error_monitoring()
        await error_monitor.start_monitoring()
        logger.info("Error monitoring system initialized successfully")

        # 注册健康检查
        health_checker.register_check("database", check_database_health)
        health_checker.register_check("websocket", check_websocket_health)
        logger.info("Health checks registered successfully")

        # 初始化数据库
        await init_db()
        logger.info("Database initialized successfully")

        # 启动实时数据服务
        if getattr(settings, 'USE_REALTIME_DATA', False):
            try:
                from app.services.realtime_data_service import get_realtime_service
                realtime_service = get_realtime_service()
                await realtime_service.start()
                logger.info("Realtime data service started successfully")
            except Exception as e:
                logger.error("Failed to start realtime data service", 
                           error=str(e), exc_info=True)

        # 初始化 Tushare 服务
        try:
            from app.services.tushare_service import init_tushare_service
            await init_tushare_service()
            logger.info("Tushare service initialized successfully")
        except Exception as e:
            logger.warning("Failed to initialize Tushare service", 
                         error=str(e))

        # 启动增强版WebSocket服务
        try:
            await enhanced_manager.start()
            logger.info("Enhanced WebSocket manager started successfully")
        except Exception as e:
            logger.warning("Failed to start enhanced WebSocket manager", error=str(e))
        
        # 启动原有WebSocket服务（如果启用）
        if getattr(settings, 'WS_ENABLED', False):
            try:
                from app.api.websocket.market_data import websocket_manager as ws_manager
                await ws_manager.start_redis_listener()
                logger.info("WebSocket market service started successfully")
            except Exception as e:
                logger.warning("Skipping WebSocket market service due to error",
                             error=str(e))

        # 记录所有注册的路由（用于调试）
        routes = [{"path": route.path, "methods": getattr(route, 'methods', [])} 
                 for route in app.routes]
        logger.info("Application routes registered", 
                   total_routes=len(routes),
                   routes=routes[:10])  # 只显示前10个路由

        # 获取并记录异常处理统计
        exception_middleware = None
        for middleware in app.user_middleware:
            if isinstance(middleware.cls, type) and issubclass(middleware.cls, ExceptionHandlerMiddleware):
                exception_middleware = middleware
                break
        
        if exception_middleware:
            logger.info("Exception handling middleware enabled")

        logger.info("Application startup completed successfully")

    except Exception as e:
        logger.critical("Application startup failed", 
                       error=str(e), exc_info=True)
        raise

    yield

    # 关闭时执行
    logger.info("Application shutdown initiated")

    try:
        # 关闭增强版WebSocket管理器
        await enhanced_manager.shutdown()
        logger.info("Enhanced WebSocket manager shutdown successfully")
        
        # 关闭原有WebSocket连接
        await websocket_manager.shutdown()
        logger.info("WebSocket manager shutdown successfully")
        
        # 停止WebSocket推送服务（已移除不存在的ws_market模块引用）
        logger.info("WebSocket services cleanup completed")

        # 关闭监控系统
        await metrics_collector.cleanup()
        logger.info("Metrics collector cleaned up successfully")
        
        # 关闭错误监控系统
        try:
            from app.core.error_monitoring import get_error_monitor
            error_monitor = get_error_monitor()
            await error_monitor.stop_monitoring()
            logger.info("Error monitoring system stopped successfully")
        except Exception as e:
            logger.warning("Error stopping error monitoring system", error=str(e))

        # 关闭数据库连接
        await cleanup_db()
        logger.info("Database connections closed successfully")

        logger.info("Application shutdown completed successfully")

    except Exception as e:
        logger.error("Error during application shutdown", 
                    error=str(e), exc_info=True)


def _create_fastapi_instance() -> FastAPI:
    """创建FastAPI实例"""
    return FastAPI(
        title=settings.PROJECT_NAME,
        description="""
        ## 专业的量化投资后端服务

        提供以下核心功能：
        - 🔐 **用户认证**: JWT令牌认证、权限管理、API密钥
        - 📊 **实时行情**: 多市场行情数据、WebSocket推送
        - 💹 **交易执行**: 订单管理、持仓查询、风险控制
        - 🎯 **策略管理**: 策略开发、回测分析、实时监控
        - 📈 **数据分析**: 技术指标、绩效分析、风险评估

        ### 技术特点
        - ⚡ 异步高性能架构
        - 🔒 金融级安全保障
        - 📱 RESTful API设计
        - 🔄 实时WebSocket通信
        - 📊 完整的API文档
        """,
        version=settings.VERSION,
        lifespan=lifespan,
        docs_url=None,
        redoc_url=None,
        openapi_url="/api/v1/openapi.json",
        contact={"name": "量化投资平台", "email": "<EMAIL>"},
        license_info={
            "name": "MIT License",
            "url": "https://opensource.org/licenses/MIT",
        },
    )


def _setup_middleware(app: FastAPI) -> None:
    """配置中间件 - 集成增强的错误处理和日志系统"""
    logger.info("Setting up application middleware")

    # 获取环境变量
    environment = getattr(settings, "ENVIRONMENT", "development")
    
    # 1. 异常处理中间件（必须最先添加，以捕获所有异常）
    app.add_middleware(
        ExceptionHandlerMiddleware,
        enable_detailed_logging=getattr(settings, "ENABLE_DETAILED_ERROR_LOGGING", True)
    )
    logger.info("Exception handling middleware added")
    
    # 2. 请求日志中间件
    try:
        request_logging_middleware = create_request_logging_middleware(
            log_request_body=getattr(settings, "LOG_REQUEST_BODY", True),
            log_response_body=getattr(settings, "LOG_RESPONSE_BODY", False),
            max_body_size=getattr(settings, "MAX_LOG_BODY_SIZE", 10240),
            enable_performance_logging=getattr(settings, "ENABLE_PERFORMANCE_LOGGING", True),
            enable_audit_logging=getattr(settings, "ENABLE_AUDIT_LOGGING", True)
        )
        app.add_middleware(request_logging_middleware)
        logger.info("Request logging middleware added")
    except Exception as e:
        logger.warning(f"Failed to add request logging middleware: {e}")
        # Skip this middleware if it fails

    # 3. CORS中间件 - 使用优化的配置
    try:
        configure_cors(app)
        
        # 调试信息
        cors_config = debug_cors_config()
        logger.info("CORS配置详情", **cors_config)
        
    except Exception as e:
        logger.error(f"CORS配置失败: {e}")
        # 备用CORS配置
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"], 
            allow_headers=["*"],
        )
        logger.warning("使用备用CORS配置")

    # 4. 安全中间件（仅在生产环境启用复杂安全功能）
    if environment == "production":
        try:
            app.add_middleware(
                SecurityMiddleware,
                enable_rate_limiting=getattr(settings, "ENABLE_RATE_LIMITING", True),
                enable_ip_whitelist=getattr(settings, "ENABLE_IP_WHITELIST", False),
            )
            logger.info("Security middleware added")
        except Exception as e:
            logger.warning("Failed to add security middleware", error=str(e))

    # 5. 受信任主机中间件（生产环境）
    if environment == "production":
        app.add_middleware(
            TrustedHostMiddleware,
            allowed_hosts=getattr(settings, "ALLOWED_HOSTS", ["*"]),
        )
        logger.info("Trusted host middleware added")

    # 6. 性能监控中间件
    try:
        if getattr(settings, "ENABLE_PERFORMANCE_MIDDLEWARE", True):
            app.add_middleware(PerformanceMiddleware, metrics_collector=metrics_collector)
            logger.info("Performance monitoring middleware added")
    except Exception as e:
        logger.warning("Failed to add performance middleware", error=str(e))

    # 7. 监控指标中间件
    try:
        if getattr(settings, "ENABLE_MONITORING_MIDDLEWARE", True):
            setup_monitoring_middleware(app)
            logger.info("Monitoring middleware added")
    except Exception as e:
        logger.warning("Failed to add monitoring middleware", error=str(e))

    # 8. 登录安全中间件（特定端点）
    try:
        if getattr(settings, "ENABLE_LOGIN_SECURITY", True):
            app.add_middleware(LoginSecurityMiddleware)
            logger.info("Login security middleware added")
    except Exception as e:
        logger.warning("Failed to add login security middleware", error=str(e))

    # 9. 响应优化中间件（解决超时问题）
    try:
        setup_response_optimization(app)
        logger.info("Response optimization middleware added")
    except Exception as e:
        logger.warning("Failed to add response optimization middleware", error=str(e))

    logger.info("Middleware setup completed successfully")


def _setup_routes(app: FastAPI) -> None:
    """配置API路由"""
    logger.info("Setting up API routes")
    
    # 配置静态文件服务 - 提供前端构建文件
    import os
    global frontend_dist
    frontend_dist = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "frontend", "dist")
    if os.path.exists(frontend_dist):
        app.mount("/static", StaticFiles(directory=frontend_dist), name="static")
        app.mount("/assets", StaticFiles(directory=os.path.join(frontend_dist, "assets")), name="assets")
        logger.info(f"Static files mounted from: {frontend_dist}")
    else:
        logger.warning(f"Frontend dist directory not found: {frontend_dist}")

    # 添加简单的WebSocket端点用于测试（在路由之前，避免中间件干扰）
    @app.websocket("/api/v1/ws")
    async def simple_websocket_endpoint(websocket: WebSocket):
        await websocket.accept()
        logger.info("Simple WebSocket connection established")

        try:
            # 发送欢迎消息
            await websocket.send_json({
                "type": "welcome",
                "message": "WebSocket连接成功",
                "timestamp": "now"
            })

            while True:
                # 接收消息
                data = await websocket.receive_text()
                import json
                message = json.loads(data)
                logger.info(f"Received WebSocket message: {message}")

                # 回显消息
                await websocket.send_json({
                    "type": "echo",
                    "original": message,
                    "timestamp": "now"
                })

        except Exception as e:
            logger.error(f"WebSocket error: {e}")
        finally:
            logger.info("WebSocket connection closed")

    # CORS调试端点
    @app.get("/api/cors/debug")
    async def cors_debug():
        """CORS配置调试端点"""
        return {
            "message": "CORS调试信息",  
            "config": debug_cors_config(),
            "headers_info": "检查响应头中的CORS相关字段"
        }

    @app.get("/api/cors/test")
    async def cors_test():
        """简单的CORS测试端点"""
        return {
            "success": True,
            "message": "CORS测试成功",
            "timestamp": "now",
            "server": "FastAPI"
        }

    # API v1 Router
    app.include_router(v1_router, prefix="/api/v1")

    # 版本检查路由
    app.include_router(version_router, prefix="/api/v1")
    logger.info("Version check routes registered")
    
    # 配置前端路由 - SPA支持
    @app.get("/{full_path:path}")
    async def serve_spa(full_path: str):
        """为SPA提供前端文件，非API路径都返回index.html"""
        # 跳过API路径
        if full_path.startswith("api/") or full_path.startswith("docs") or full_path.startswith("redoc"):
            raise HTTPException(status_code=404, detail="Not found")
            
        # 检查是否是静态资源
        if full_path.startswith("assets/") or full_path.endswith(('.js', '.css', '.png', '.jpg', '.ico', '.svg')):
            static_path = os.path.join(frontend_dist, full_path)
            if os.path.exists(static_path):
                return FileResponse(static_path)
            raise HTTPException(status_code=404, detail="Static file not found")
            
        # 其他所有路径返回index.html（Vue Router处理）
        index_path = os.path.join(frontend_dist, "index.html")
        if os.path.exists(index_path):
            return FileResponse(index_path, media_type="text/html")
        else:
            return JSONResponse({"error": "Frontend not built"}, status_code=500)


def _setup_exception_handlers(app: FastAPI) -> None:
    """配置异常处理器 - 增强版本"""
    logger.info("Setting up exception handlers")

    @app.exception_handler(BaseCustomException)
    async def custom_exception_handler(request: Request, exc: BaseCustomException):
        """处理增强的自定义异常"""
        logger.warning("Custom exception occurred", 
                      exception_type=type(exc).__name__,
                      error_code=exc.error_code,
                      message=exc.message,
                      severity=exc.severity.value,
                      category=exc.category.value if exc.category else None)
        
        return JSONResponse(
            status_code=400,  # 默认状态码，中间件会根据异常类型调整
            content=exc.to_dict()
        )

    @app.exception_handler(QuantPlatformException)
    async def quant_platform_exception_handler(
        request: Request, exc: QuantPlatformException
    ):
        """处理旧版自定义业务异常（向后兼容）"""
        logger.warning("Legacy business exception occurred", 
                      error_code=exc.error_code,
                      message=exc.message)
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "type": "legacy_business_error",
                    "code": exc.error_code,
                    "message": exc.message,
                    "details": exc.details,
                    "request_id": getattr(request.state, 'request_id', 'unknown'),
                    "timestamp": str(datetime.utcnow().isoformat())
                }
            },
        )

    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """处理FastAPI HTTP异常"""
        logger.warning("HTTP exception occurred", 
                      status_code=exc.status_code,
                      detail=exc.detail)
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": {
                    "type": "http_error",
                    "code": f"HTTP_{exc.status_code}",
                    "message": exc.detail,
                    "status_code": exc.status_code,
                    "request_id": getattr(request.state, 'request_id', 'unknown'),
                    "timestamp": str(datetime.utcnow().isoformat())
                }
            },
        )

    @app.exception_handler(Exception)
    async def global_exception_handler(request: Request, exc: Exception):
        """全局兜底异常处理器"""
        # 注意：这个处理器应该很少被触发，因为大部分异常会被中间件处理
        logger.error("Unhandled exception reached global handler", 
                    exception_type=type(exc).__name__,
                    message=str(exc),
                    exc_info=True)
        
        debug_mode = getattr(settings, "DEBUG", False)
        
        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "type": "internal_server_error",
                    "code": "UNHANDLED_EXCEPTION",
                    "message": str(exc) if debug_mode else "服务器内部错误",
                    "request_id": getattr(request.state, 'request_id', 'unknown'),
                    "timestamp": str(datetime.utcnow().isoformat()),
                    "debug_info": {
                        "exception_type": type(exc).__name__,
                        "traceback": str(exc) if debug_mode else None
                    } if debug_mode else None
                }
            },
        )
    
    logger.info("Exception handlers configured successfully")


def _setup_openapi(app: FastAPI) -> None:
    """配置OpenAPI文档"""

    def custom_openapi():
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
        )

        # 添加安全定义
        openapi_schema["components"]["securitySchemes"] = {
            "bearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT",
                "description": "输入JWT令牌进行认证",
            }
        }
        # 全局应用安全方案
        openapi_schema["security"] = [{"bearerAuth": []}]

        return openapi_schema

    app.openapi = custom_openapi


def _setup_docs(app: FastAPI) -> None:
    """配置API文档页面"""

    @app.get("/docs", include_in_schema=False)
    async def custom_swagger_ui_html():
        """自定义Swagger UI页面"""
        return get_swagger_ui_html(
            openapi_url=app.openapi_url,
            title=f"{app.title} - API Docs",
            swagger_favicon_url="/static/favicon.ico",
        )

    @app.get("/redoc", include_in_schema=False)
    async def redoc_html():
        """自定义ReDoc页面"""
        return get_redoc_html(
            openapi_url=app.openapi_url,
            title=f"{app.title} - ReDoc",
            redoc_favicon_url="/static/favicon.ico",
        )


def _setup_basic_routes(app: FastAPI) -> None:
    """配置基础路由"""

    @app.get("/", tags=["系统"])
    async def root():
        """
        根路径，返回项目信息。
        """
        return {
            "project": settings.PROJECT_NAME,
            "version": settings.VERSION,
            "description": "Welcome to the Quant Platform API.",
            "docs": "/docs",
            "redoc": "/redoc",
        }

    @app.get("/health", tags=["系统"])
    async def health_check_endpoint():
        """健康检查"""
        try:
            # 简化的健康检查，避免复杂的依赖
            from datetime import datetime
            return {
                "status": "healthy",
                "timestamp": datetime.now().isoformat(),
                "version": settings.VERSION,
                "project": settings.PROJECT_NAME,
                "services": {
                    "api": "running",
                    "database": "connected"
                }
            }
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    @app.get("/ready", tags=["系统"])
    async def readiness_check_endpoint():
        """就绪检查"""
        return await readiness_check(health_checker)

    @app.get("/live", tags=["系统"])
    async def liveness_check_endpoint():
        """存活检查"""
        return await liveness_check()

    @app.get("/api-debug", tags=["系统"])
    async def api_debug_endpoint():
        """API路由调试"""
        v1_routes = []
        try:
            for route in v1_router.routes:
                v1_routes.append({
                    "path": route.path,
                    "methods": getattr(route, 'methods', ['Unknown']),
                    "name": getattr(route, 'name', 'unknown')
                })
        except Exception as e:
            return {"error": f"Failed to get v1 routes: {e}"}
        
        app_routes = []
        for route in app.routes:
            app_routes.append({
                "path": route.path,
                "methods": getattr(route, 'methods', ['Unknown']),
                "name": getattr(route, 'name', 'unknown')
            })
            
        return {
            "v1_routes_count": len(v1_routes),
            "app_routes_count": len(app_routes), 
            "v1_sample": v1_routes[:5],
            "app_sample": app_routes[:10]
        }


def create_app() -> FastAPI:
    """
    创建并配置FastAPI应用实例
    """
    app = _create_fastapi_instance()
    _setup_middleware(app)
    _setup_routes(app)
    _setup_exception_handlers(app)
    _setup_openapi(app)
    _setup_docs(app)
    _setup_basic_routes(app)
    return app


app = create_app()

if __name__ == "__main__":
    # 使用uvicorn运行 - 优化超时配置
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        workers=1,
        log_level=settings.LOG_LEVEL.lower(),
        # 添加超时配置以避免408错误
        timeout_keep_alive=30,  # Keep-alive超时
        timeout_graceful_shutdown=10,  # 优雅关闭超时
        limit_concurrency=1000,  # 并发限制
        limit_max_requests=10000,  # 最大请求数
        backlog=2048,  # 连接队列大小
    )
