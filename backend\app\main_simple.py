"""
简化版量化投资平台后端主应用
基于main_fixed.py的简化版本，用于快速启动和测试
"""

import logging
import os
import sys
import random
import time
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import uvicorn
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

# 预导入常用模块以减少运行时导入开销
import json
import asyncio

# 简单的内存缓存
class SimpleCache:
    def __init__(self, ttl_seconds=60):
        self.cache = {}
        self.ttl = ttl_seconds

    def get(self, key):
        if key in self.cache:
            data, timestamp = self.cache[key]
            if time.time() - timestamp < self.ttl:
                return data
            else:
                del self.cache[key]
        return None

    def set(self, key, value):
        self.cache[key] = (value, time.time())

    def clear(self):
        self.cache.clear()

# 全局缓存实例
api_cache = SimpleCache(ttl_seconds=30)  # 30秒缓存

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/simple_backend.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 确保日志目录存在
os.makedirs('logs', exist_ok=True)

# 数据库配置
DATABASE_URL = "sqlite+aiosqlite:///./data/quantplatform.db"
engine = None
SessionLocal = None

# 确保数据目录存在
os.makedirs('data', exist_ok=True)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global engine, SessionLocal
    
    logger.info("🚀 启动量化投资平台后端服务...")
    
    # 初始化数据库
    try:
        engine = create_async_engine(DATABASE_URL, echo=False)
        SessionLocal = async_sessionmaker(engine, expire_on_commit=False)
        logger.info("📊 数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
    
    # 初始化缓存
    api_cache.clear()
    logger.info("🔄 缓存初始化完成")
    
    logger.info("✅ 后端服务启动完成")
    
    yield
    
    # 清理资源
    if engine:
        await engine.dispose()
    logger.info("🔄 资源清理完成")

# 创建FastAPI应用
app = FastAPI(
    title="量化投资平台 API (简化版)",
    description="简化版量化投资平台后端API服务",
    version="1.0.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 基础路由
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "量化投资平台 API (简化版)",
        "version": "1.0.0",
        "status": "running",
        "timestamp": datetime.now().isoformat()
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "database": "connected" if engine else "disconnected"
    }

# 简化的API路由
@app.get("/api/v1/health")
async def api_health():
    """API健康检查"""
    return {
        "status": "healthy",
        "api_version": "v1",
        "timestamp": datetime.now().isoformat()
    }

# 简化的认证路由
from pydantic import BaseModel

class LoginRequest(BaseModel):
    username: str
    password: str

@app.post("/api/v1/auth/login")
async def login(login_data: LoginRequest):
    """简化的登录接口"""
    try:
        username = login_data.username
        password = login_data.password

        # 简单的演示账号验证
        if username == "admin" and password == "admin":
            # 生成简单的token
            token = f"demo_token_{int(time.time())}"

            user_info = {
                "id": 1,
                "username": "admin",
                "email": "<EMAIL>",
                "role": "admin",
                "permissions": ["*"]
            }

            return {
                "user": user_info,
                "token": token,
                "message": "登录成功"
            }
        else:
            raise HTTPException(status_code=401, detail="用户名或密码错误")

    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(status_code=400, detail="登录请求处理失败")

@app.get("/api/v1/auth/me")
async def get_current_user(request: Request):
    """获取当前用户信息"""
    auth_header = request.headers.get("Authorization")

    if not auth_header or not auth_header.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="未提供认证token")

    token = auth_header.replace("Bearer ", "")

    # 简单的token验证
    if token.startswith("demo_token_"):
        return {
            "id": 1,
            "username": "admin",
            "email": "<EMAIL>",
            "role": "admin",
            "permissions": ["*"]
        }
    else:
        raise HTTPException(status_code=401, detail="无效的token")

@app.get("/api/v1/market/stocks")
async def get_stocks():
    """获取股票列表"""
    # 简单的模拟数据
    stocks = [
        {"symbol": "000001", "name": "平安银行", "price": 12.50, "change": 0.05},
        {"symbol": "000002", "name": "万科A", "price": 18.30, "change": -0.12},
        {"symbol": "600000", "name": "浦发银行", "price": 8.90, "change": 0.08},
        {"symbol": "600036", "name": "招商银行", "price": 35.20, "change": 0.15},
        {"symbol": "000858", "name": "五粮液", "price": 128.50, "change": -2.30}
    ]
    
    return {
        "code": 200,
        "message": "success",
        "data": stocks,
        "timestamp": datetime.now().isoformat()
    }

# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"全局异常: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "code": 500,
            "message": "内部服务器错误",
            "detail": str(exc) if os.getenv("DEBUG") else "服务器内部错误"
        }
    )

if __name__ == "__main__":
    # 检查端口是否被占用
    import socket
    
    def is_port_in_use(port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(('localhost', port)) == 0
    
    port = 8000
    if is_port_in_use(port):
        logger.warning(f"⚠️ 端口{port}已被占用，尝试使用其他端口...")
        port = 8001
        logger.info(f"🌐 服务将在端口 {port} 启动")
    
    logger.info(f"📚 API文档: http://localhost:{port}/docs")
    logger.info(f"🔍 健康检查: http://localhost:{port}/health")
    
    uvicorn.run(
        "app.main_simple:app",
        host="0.0.0.0",
        port=port,
        reload=True,
        log_level="info"
    )
